# DOI PDF下载器 - 完整使用指南

## 📁 文件说明

我为您创建了以下文件来处理DOI地址并下载PDF文件：

### 核心文件
1. **`doi_pdf_downloader.py`** - 完整版DOI下载器（功能最全面）
2. **`simple_doi_downloader.py`** - 简化版DOI下载器（推荐使用）
3. **`run_downloader.py`** - 交互式启动脚本（最易用）

### 辅助文件
4. **`test_doi_downloader.py`** - 测试脚本
5. **`download_dois.bat`** - Windows批处理脚本
6. **`sample_dois.txt`** - 示例DOI文件
7. **`README_DOI_Downloader.md`** - 详细说明文档

## 🚀 快速开始

### 方法1：使用交互式启动脚本（推荐）
```bash
python run_downloader.py
```
这个脚本会：
- 自动检查依赖包
- 查找DOI文件
- 提供交互式界面
- 自动运行下载器

### 方法2：直接使用简化版下载器
```bash
python simple_doi_downloader.py <DOI文件> [下载目录]
```
示例：
```bash
python simple_doi_downloader.py sample_dois.txt downloads
```

### 方法3：使用Windows批处理文件
将包含DOI的文本文件拖拽到 `download_dois.bat` 文件上即可。

## 📝 DOI文件格式

创建一个文本文件（如 `dois.txt`），每行包含一个DOI地址：

```
10.1038/nature12345
10.1126/science.abc123
10.1016/j.cell.2023.01.001
10.1038/s41586-023-00000-0
10.1126/science.ade1702
```

## 🔧 安装依赖

首次使用前，请安装必要的Python包：

```bash
pip install requests
```

## 📊 功能特点

### ✅ 支持的功能
- 自动从文本文件中读取DOI地址
- 智能提取PDF下载链接
- 多源下载（DOI.org + Sci-Hub）
- 自动创建下载目录
- 文件名自动清理
- 详细的日志记录
- 错误处理和重试机制
- 下载进度显示

### ⚠️ 注意事项
- 某些PDF可能需要机构访问权限
- 下载速度包含延迟，避免被服务器限制
- 请遵守相关版权法规
- 建议在正常网络环境下测试后再部署到学校网络

## 🛠️ 故障排除

### 常见问题

1. **下载失败**
   - 检查网络连接
   - 确认DOI地址正确
   - 查看日志文件了解详细错误

2. **依赖包错误**
   ```bash
   pip install requests
   ```

3. **文件权限问题**
   - 确保有写入权限
   - 检查磁盘空间

4. **网络连接问题**
   - 某些学术资源可能需要VPN
   - 尝试手动访问DOI链接确认可访问性

## 📈 使用示例

### 步骤1：准备DOI文件
创建 `my_dois.txt`：
```
10.1038/nature12345
10.1126/science.abc123
```

### 步骤2：运行下载器
```bash
python run_downloader.py
```

### 步骤3：查看结果
- PDF文件保存在 `downloads` 目录
- 查看 `doi_download.log` 了解详细过程

## 🔍 测试功能

运行测试脚本验证功能：
```bash
python test_doi_downloader.py
```

## 📞 技术支持

如果遇到问题：
1. 查看日志文件 `doi_download.log`
2. 运行测试脚本检查环境
3. 确认DOI地址格式正确
4. 检查网络连接状态

## 📋 文件清单

```
📁 DOI下载器项目
├── 📄 doi_pdf_downloader.py      # 完整版下载器
├── 📄 simple_doi_downloader.py   # 简化版下载器（推荐）
├── 📄 run_downloader.py          # 交互式启动脚本
├── 📄 test_doi_downloader.py     # 测试脚本
├── 📄 download_dois.bat          # Windows批处理
├── 📄 sample_dois.txt            # 示例DOI文件
├── 📄 README_DOI_Downloader.md   # 详细说明
├── 📄 使用说明.md                # 本文件
└── 📁 downloads/                 # 下载目录（自动创建）
```

## 🎯 推荐使用流程

1. **首次使用**：运行 `python run_downloader.py`
2. **日常使用**：直接运行 `python simple_doi_downloader.py <文件>`
3. **批量处理**：使用批处理文件 `download_dois.bat`
4. **问题排查**：运行 `python test_doi_downloader.py`

祝您使用愉快！🎉 