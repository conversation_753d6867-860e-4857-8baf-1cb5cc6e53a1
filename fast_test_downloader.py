#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速DOI下载测试器
"""

import requests
import os
import sys
import time
import re
import json
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse

class FastDOITester:
    def __init__(self, download_dir="downloads"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'no_pdf': 0,
            'start_time': datetime.now(),
            'details': []
        }
    
    def get_pdf_url(self, doi):
        """获取PDF链接 - 优化版本"""
        # 优先尝试Sci-Hub.ru，通常成功率较高
        scihub_urls = [
            f"https://sci-hub.ru/{doi}",
            f"https://sci-hub.se/{doi}",
            f"https://sci-hub.st/{doi}"
        ]
        
        for scihub_url in scihub_urls:
            try:
                response = self.session.get(scihub_url, timeout=15)
                
                if response.status_code == 200:
                    # 查找PDF链接 - 多种模式
                    pdf_patterns = [
                        r'href=["\']([^"\']*\.pdf[^"\']*)["\']',
                        r'<iframe[^>]*src=["\']([^"\']*\.pdf[^"\']*)["\']',
                        r'<embed[^>]*src=["\']([^"\']*\.pdf[^"\']*)["\']'
                    ]
                    
                    for pattern in pdf_patterns:
                        matches = re.findall(pattern, response.text, re.IGNORECASE)
                        for match in matches:
                            if match.startswith('http'):
                                return match
                            elif match.startswith('//'):
                                return 'https:' + match
                            elif match.startswith('/'):
                                parsed = urlparse(scihub_url)
                                return f"{parsed.scheme}://{parsed.netloc}{match}"
                            else:
                                return f"{scihub_url.rstrip('/')}/{match}"
                                
            except Exception:
                continue
        
        return None
    
    def download_pdf(self, pdf_url, filename):
        """下载PDF - 优化版本"""
        try:
            response = self.session.get(pdf_url, stream=True, timeout=30)
            response.raise_for_status()
            
            file_path = self.download_dir / filename
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            file_size = file_path.stat().st_size
            if file_size > 5000:  # 至少5KB
                return True, file_size
            else:
                os.remove(file_path)
                return False, file_size
                
        except Exception:
            return False, 0
    
    def test_dois(self, input_file, max_tests=50):
        """测试DOI下载"""
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
        
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📁 测试文件: {input_file}")
        print(f"📊 总行数: {len(lines)}")
        print(f"🎯 测试数量: {min(max_tests, len(lines))}")
        print("=" * 60)
        
        test_count = 0
        for line_num, line in enumerate(lines, 1):
            if test_count >= max_tests:
                break
                
            line = line.strip()
            if not line:
                continue
            
            print(f"[{test_count + 1}/{max_tests}] {line}")
            
            # 提取DOI
            doi_pattern = r'10\.\d{4,}/[-._;()/:\w]+'
            matches = re.findall(doi_pattern, line)
            
            if not matches:
                print("  ❌ 未找到DOI")
                self.stats['failed'] += 1
                continue
            
            test_count += 1
            self.stats['total'] += 1
            
            doi = matches[0]
            
            # 获取PDF链接
            pdf_url = self.get_pdf_url(doi)
            
            if not pdf_url:
                print("  ❌ 无法获取PDF链接")
                self.stats['no_pdf'] += 1
                self.stats['details'].append({
                    'doi': doi,
                    'status': 'no_pdf_url'
                })
                continue
            
            # 下载PDF
            safe_doi = doi.replace('/', '_').replace(':', '_')
            filename = f"{safe_doi}.pdf"
            
            success, file_size = self.download_pdf(pdf_url, filename)
            
            if success:
                print(f"  ✅ 成功: {filename} ({file_size} bytes)")
                self.stats['success'] += 1
                self.stats['details'].append({
                    'doi': doi,
                    'status': 'success',
                    'filename': filename,
                    'size': file_size
                })
            else:
                print(f"  ❌ 下载失败: {file_size} bytes")
                self.stats['failed'] += 1
                self.stats['details'].append({
                    'doi': doi,
                    'status': 'download_failed',
                    'size': file_size
                })
            
            time.sleep(1)  # 减少延迟
        
        # 生成报告
        self.print_report()
    
    def print_report(self):
        """打印统计报告"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        print("\n" + "=" * 60)
        print("📊 测试结果统计")
        print("=" * 60)
        print(f"总测试数: {self.stats['total']}")
        print(f"成功下载: {self.stats['success']}")
        print(f"下载失败: {self.stats['failed']}")
        print(f"无PDF链接: {self.stats['no_pdf']}")
        
        if self.stats['total'] > 0:
            success_rate = (self.stats['success'] / self.stats['total']) * 100
            print(f"成功率: {success_rate:.1f}%")
            
            pdf_available_rate = ((self.stats['success'] + self.stats['failed']) / self.stats['total']) * 100
            print(f"PDF可用率: {pdf_available_rate:.1f}%")
        
        print(f"耗时: {duration}")
        print(f"下载目录: {self.download_dir}")
        
        # 分析DOI格式的成功率
        self.analyze_doi_patterns()
        
        # 保存详细报告
        report_file = f"fast_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2)
        
        print(f"详细报告: {report_file}")
    
    def analyze_doi_patterns(self):
        """分析不同DOI格式的成功率"""
        print("\n📈 DOI格式分析:")
        
        # 按年份分析
        year_stats = {}
        for detail in self.stats['details']:
            doi = detail['doi']
            # 提取年份
            year_match = re.search(r'20\d{2}', doi)
            if year_match:
                year = year_match.group()
                if year not in year_stats:
                    year_stats[year] = {'success': 0, 'total': 0}
                year_stats[year]['total'] += 1
                if detail['status'] == 'success':
                    year_stats[year]['success'] += 1
        
        for year in sorted(year_stats.keys()):
            stats = year_stats[year]
            if stats['total'] > 0:
                rate = (stats['success'] / stats['total']) * 100
                print(f"  {year}年: {stats['success']}/{stats['total']} ({rate:.1f}%)")
        
        # 按DOI格式分析
        format_stats = {}
        for detail in self.stats['details']:
            doi = detail['doi']
            if 'S0304-8853' in doi:
                format_type = 'S0304-8853格式'
            elif 'j.jmmm' in doi:
                format_type = 'j.jmmm格式'
            else:
                format_type = '其他格式'
            
            if format_type not in format_stats:
                format_stats[format_type] = {'success': 0, 'total': 0}
            format_stats[format_type]['total'] += 1
            if detail['status'] == 'success':
                format_stats[format_type]['success'] += 1
        
        print("\n📊 格式分析:")
        for format_type, stats in format_stats.items():
            if stats['total'] > 0:
                rate = (stats['success'] / stats['total']) * 100
                print(f"  {format_type}: {stats['success']}/{stats['total']} ({rate:.1f}%)")

def main():
    if len(sys.argv) < 2:
        print("用法: python fast_test_downloader.py <DOI文件> [下载目录] [测试数量]")
        print("示例: python fast_test_downloader.py sample_dois.txt downloads 50")
        sys.exit(1)
    
    input_file = sys.argv[1]
    download_dir = sys.argv[2] if len(sys.argv) > 2 else "downloads"
    max_tests = int(sys.argv[3]) if len(sys.argv) > 3 else 50
    
    tester = FastDOITester(download_dir)
    tester.test_dois(input_file, max_tests)

if __name__ == "__main__":
    main() 