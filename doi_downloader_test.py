#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOI下载器 - 测试版本
专门用于测试大量DOI的下载成功率
"""

import requests
import os
import sys
import time
import re
import json
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse, parse_qs

class DOIDownloaderTest:
    def __init__(self, download_dir="downloads"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 统计信息
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': datetime.now(),
            'details': []
        }
        
    def extract_doi_from_text(self, text):
        """从文本中提取DOI"""
        doi_pattern = r'10\.\d{4,}/[-._;()/:\w]+'
        matches = re.findall(doi_pattern, text)
        return matches
    
    def get_pdf_url_from_doi(self, doi):
        """通过DOI获取PDF下载链接"""
        methods = [
            self._try_scihub,
            self._try_doi_org,
            self._try_arxiv
        ]
        
        for method in methods:
            try:
                pdf_url = method(doi)
                if pdf_url:
                    return pdf_url
            except Exception as e:
                print(f"  ⚠️ 方法失败: {e}")
                continue
        
        return None
    
    def _try_scihub(self, doi):
        """尝试通过Sci-Hub获取PDF"""
        scihub_urls = [
            f"https://sci-hub.se/{doi}",
            f"https://sci-hub.st/{doi}",
            f"https://sci-hub.ru/{doi}"
        ]
        
        for scihub_url in scihub_urls:
            try:
                print(f"  🔍 尝试Sci-Hub: {scihub_url}")
                response = self.session.get(scihub_url, timeout=30)
                
                if response.status_code == 200:
                    # 查找PDF链接
                    pdf_patterns = [
                        r'href=["\']([^"\']*\.pdf[^"\']*)["\']',
                        r'href=["\']([^"\']*pdf[^"\']*)["\']',
                        r'<iframe[^>]*src=["\']([^"\']*\.pdf[^"\']*)["\']'
                    ]
                    
                    for pattern in pdf_patterns:
                        matches = re.findall(pattern, response.text, re.IGNORECASE)
                        for match in matches:
                            if match.startswith('http'):
                                return match
                            elif match.startswith('//'):
                                return 'https:' + match
                            elif match.startswith('/'):
                                parsed = urlparse(scihub_url)
                                return f"{parsed.scheme}://{parsed.netloc}{match}"
                            else:
                                return f"{scihub_url.rstrip('/')}/{match}"
                                
            except Exception as e:
                print(f"  ⚠️ Sci-Hub {scihub_url} 失败: {e}")
                continue
        
        return None
    
    def _try_doi_org(self, doi):
        """尝试通过DOI.org获取PDF"""
        try:
            doi_url = f"https://doi.org/{doi}"
            print(f"  🔍 尝试DOI.org: {doi_url}")
            
            response = self.session.get(doi_url, allow_redirects=True, timeout=30)
            
            if response.status_code == 200:
                # 检查是否是PDF直接链接
                if response.headers.get('content-type', '').startswith('application/pdf'):
                    return doi_url
                
                # 尝试从页面中提取PDF链接
                pdf_links = self.extract_pdf_links(response.text, response.url)
                if pdf_links:
                    return pdf_links[0]
                    
        except Exception as e:
            print(f"  ⚠️ DOI.org失败: {e}")
        
        return None
    
    def _try_arxiv(self, doi):
        """尝试通过arXiv获取PDF（如果DOI是arXiv格式）"""
        if 'arxiv' in doi.lower():
            try:
                arxiv_id = doi.split('/')[-1]
                pdf_url = f"https://arxiv.org/pdf/{arxiv_id}.pdf"
                print(f"  🔍 尝试arXiv: {pdf_url}")
                
                response = self.session.head(pdf_url, timeout=10)
                if response.status_code == 200:
                    return pdf_url
                    
            except Exception as e:
                print(f"  ⚠️ arXiv失败: {e}")
        
        return None
    
    def extract_pdf_links(self, html_content, base_url):
        """从HTML内容中提取PDF链接"""
        pdf_links = []
        
        pdf_patterns = [
            r'href=["\']([^"\']*\.pdf[^"\']*)["\']',
            r'href=["\']([^"\']*pdf[^"\']*)["\']',
            r'src=["\']([^"\']*\.pdf[^"\']*)["\']'
        ]
        
        for pattern in pdf_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if match.startswith('http'):
                    pdf_links.append(match)
                elif match.startswith('/'):
                    parsed_base = urlparse(base_url)
                    pdf_links.append(f"{parsed_base.scheme}://{parsed_base.netloc}{match}")
                else:
                    pdf_links.append(f"{base_url.rstrip('/')}/{match}")
        
        return list(set(pdf_links))
    
    def download_pdf(self, pdf_url, filename):
        """下载PDF文件"""
        try:
            print(f"  📥 正在下载: {pdf_url}")
            response = self.session.get(pdf_url, stream=True, timeout=60)
            response.raise_for_status()
            
            # 检查内容类型
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('application/pdf'):
                print(f"  ⚠️ 内容类型不是PDF: {content_type}")
            
            file_path = self.download_dir / filename
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            file_size = file_path.stat().st_size
            if file_size > 1000:  # 至少1KB
                print(f"  ✅ 下载完成: {filename} ({file_size} bytes)")
                return True
            else:
                print(f"  ❌ 文件太小，可能不是有效PDF: {file_size} bytes")
                os.remove(file_path)
                return False
                
        except Exception as e:
            print(f"  ❌ 下载失败: {e}")
            return False
    
    def sanitize_filename(self, filename):
        """清理文件名"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename[:200]
    
    def process_doi_file(self, input_file, max_tests=50):
        """处理DOI文件并测试下载成功率"""
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
        
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📁 开始处理文件: {input_file}")
        print(f"📊 总行数: {len(lines)}")
        print(f"🎯 测试数量: {min(max_tests, len(lines))}")
        print("=" * 60)
        
        test_count = 0
        for line_num, line in enumerate(lines, 1):
            if test_count >= max_tests:
                break
                
            line = line.strip()
            if not line:
                continue
            
            print(f"\n[{test_count + 1}/{max_tests}] 处理第 {line_num} 行: {line}")
            
            # 提取DOI
            dois = self.extract_doi_from_text(line)
            
            if not dois:
                print(f"  ❌ 未找到DOI")
                self.stats['failed'] += 1
                self.stats['details'].append({
                    'line': line_num,
                    'doi': line,
                    'status': 'no_doi_found',
                    'error': '未找到有效DOI'
                })
                continue
            
            test_count += 1
            self.stats['total'] += 1
            
            for doi in dois:
                try:
                    print(f"  🔍 处理DOI: {doi}")
                    
                    # 获取PDF链接
                    pdf_url = self.get_pdf_url_from_doi(doi)
                    
                    if not pdf_url:
                        print(f"  ❌ 无法获取PDF链接")
                        self.stats['failed'] += 1
                        self.stats['details'].append({
                            'line': line_num,
                            'doi': doi,
                            'status': 'no_pdf_url',
                            'error': '无法获取PDF链接'
                        })
                        continue
                    
                    # 生成文件名
                    safe_doi = doi.replace('/', '_').replace(':', '_')
                    filename = f"{safe_doi}.pdf"
                    filename = self.sanitize_filename(filename)
                    
                    # 下载PDF
                    if self.download_pdf(pdf_url, filename):
                        self.stats['success'] += 1
                        self.stats['details'].append({
                            'line': line_num,
                            'doi': doi,
                            'status': 'success',
                            'filename': filename,
                            'url': pdf_url
                        })
                        print(f"  ✅ 成功下载: {filename}")
                    else:
                        self.stats['failed'] += 1
                        self.stats['details'].append({
                            'line': line_num,
                            'doi': doi,
                            'status': 'download_failed',
                            'error': 'PDF下载失败'
                        })
                        print(f"  ❌ 下载失败")
                    
                    # 添加延迟
                    time.sleep(2)
                    break  # 只处理第一个DOI
                    
                except Exception as e:
                    print(f"  ❌ 处理失败: {e}")
                    self.stats['failed'] += 1
                    self.stats['details'].append({
                        'line': line_num,
                        'doi': doi,
                        'status': 'error',
                        'error': str(e)
                    })
        
        # 生成统计报告
        self.generate_report()
    
    def generate_report(self):
        """生成下载统计报告"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        print("\n" + "=" * 60)
        print("📊 下载统计报告")
        print("=" * 60)
        print(f"总测试数: {self.stats['total']}")
        print(f"成功下载: {self.stats['success']}")
        print(f"下载失败: {self.stats['failed']}")
        print(f"跳过数量: {self.stats['skipped']}")
        
        if self.stats['total'] > 0:
            success_rate = (self.stats['success'] / self.stats['total']) * 100
            print(f"成功率: {success_rate:.1f}%")
        
        print(f"耗时: {duration}")
        print(f"下载目录: {self.download_dir}")
        
        # 保存详细报告
        report_file = f"download_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2)
        
        print(f"详细报告已保存: {report_file}")
        
        # 显示前10个成功和失败的例子
        success_details = [d for d in self.stats['details'] if d['status'] == 'success']
        failed_details = [d for d in self.stats['details'] if d['status'] != 'success']
        
        if success_details:
            print(f"\n✅ 成功示例 (前5个):")
            for i, detail in enumerate(success_details[:5], 1):
                print(f"  {i}. {detail['doi']} -> {detail['filename']}")
        
        if failed_details:
            print(f"\n❌ 失败示例 (前5个):")
            for i, detail in enumerate(failed_details[:5], 1):
                print(f"  {i}. {detail['doi']} - {detail.get('error', '未知错误')}")

def main():
    if len(sys.argv) < 2:
        print("用法: python doi_downloader_test.py <DOI文件> [下载目录] [测试数量]")
        print("示例: python doi_downloader_test.py sample_dois.txt downloads 50")
        sys.exit(1)
    
    input_file = sys.argv[1]
    download_dir = sys.argv[2] if len(sys.argv) > 2 else "downloads"
    max_tests = int(sys.argv[3]) if len(sys.argv) > 3 else 50
    
    downloader = DOIDownloaderTest(download_dir)
    downloader.process_doi_file(input_file, max_tests)

if __name__ == "__main__":
    main() 