#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版DOI下载器
基于测试结果优化，针对您的DOI文件特点进行优化
"""

import requests
import os
import sys
import time
import re
import json
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse

class FinalDOIDownloader:
    def __init__(self, download_dir="downloads"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': datetime.now(),
            'details': []
        }
    
    def get_pdf_url(self, doi):
        """获取PDF链接 - 优化版本"""
        # 基于测试结果，优先使用sci-hub.ru
        scihub_urls = [
            f"https://sci-hub.ru/{doi}",
            f"https://sci-hub.se/{doi}",
            f"https://sci-hub.st/{doi}"
        ]
        
        for scihub_url in scihub_urls:
            try:
                response = self.session.get(scihub_url, timeout=15)
                
                if response.status_code == 200:
                    # 多种PDF链接模式
                    pdf_patterns = [
                        r'href=["\']([^"\']*\.pdf[^"\']*)["\']',
                        r'<iframe[^>]*src=["\']([^"\']*\.pdf[^"\']*)["\']',
                        r'<embed[^>]*src=["\']([^"\']*\.pdf[^"\']*)["\']'
                    ]
                    
                    for pattern in pdf_patterns:
                        matches = re.findall(pattern, response.text, re.IGNORECASE)
                        for match in matches:
                            if match.startswith('http'):
                                return match
                            elif match.startswith('//'):
                                return 'https:' + match
                            elif match.startswith('/'):
                                parsed = urlparse(scihub_url)
                                return f"{parsed.scheme}://{parsed.netloc}{match}"
                            else:
                                return f"{scihub_url.rstrip('/')}/{match}"
                                
            except Exception:
                continue
        
        return None
    
    def download_pdf(self, pdf_url, filename):
        """下载PDF文件"""
        try:
            response = self.session.get(pdf_url, stream=True, timeout=60)
            response.raise_for_status()
            
            file_path = self.download_dir / filename
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            file_size = file_path.stat().st_size
            if file_size > 5000:  # 至少5KB
                return True, file_size
            else:
                os.remove(file_path)
                return False, file_size
                
        except Exception as e:
            return False, 0
    
    def sanitize_filename(self, filename):
        """清理文件名"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename[:200]
    
    def process_doi_file(self, input_file, max_downloads=None):
        """处理DOI文件"""
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
        
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📁 处理文件: {input_file}")
        print(f"📊 总行数: {len(lines)}")
        if max_downloads:
            print(f"🎯 最大下载数: {max_downloads}")
        print("=" * 60)
        
        download_count = 0
        for line_num, line in enumerate(lines, 1):
            if max_downloads and download_count >= max_downloads:
                break
                
            line = line.strip()
            if not line:
                continue
            
            # 提取DOI
            doi_pattern = r'10\.\d{4,}/[-._;()/:\w]+'
            matches = re.findall(doi_pattern, line)
            
            if not matches:
                print(f"[{line_num}] ❌ 未找到DOI: {line}")
                self.stats['skipped'] += 1
                continue
            
            doi = matches[0]
            print(f"[{line_num}] 🔍 {doi}")
            
            # 获取PDF链接
            pdf_url = self.get_pdf_url(doi)
            
            if not pdf_url:
                print(f"  ❌ 无法获取PDF链接")
                self.stats['failed'] += 1
                self.stats['details'].append({
                    'line': line_num,
                    'doi': doi,
                    'status': 'no_pdf_url'
                })
                continue
            
            # 下载PDF
            safe_doi = doi.replace('/', '_').replace(':', '_')
            filename = f"{safe_doi}.pdf"
            filename = self.sanitize_filename(filename)
            
            success, file_size = self.download_pdf(pdf_url, filename)
            
            if success:
                print(f"  ✅ 成功: {filename} ({file_size} bytes)")
                self.stats['success'] += 1
                download_count += 1
                self.stats['details'].append({
                    'line': line_num,
                    'doi': doi,
                    'status': 'success',
                    'filename': filename,
                    'size': file_size
                })
            else:
                print(f"  ❌ 下载失败")
                self.stats['failed'] += 1
                self.stats['details'].append({
                    'line': line_num,
                    'doi': doi,
                    'status': 'download_failed'
                })
            
            self.stats['total'] += 1
            
            # 添加延迟
            time.sleep(1.5)
        
        # 生成报告
        self.print_report()
    
    def print_report(self):
        """打印统计报告"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        print("\n" + "=" * 60)
        print("📊 下载完成统计")
        print("=" * 60)
        print(f"总处理数: {self.stats['total']}")
        print(f"成功下载: {self.stats['success']}")
        print(f"下载失败: {self.stats['failed']}")
        print(f"跳过数量: {self.stats['skipped']}")
        
        if self.stats['total'] > 0:
            success_rate = (self.stats['success'] / self.stats['total']) * 100
            print(f"成功率: {success_rate:.1f}%")
        
        print(f"耗时: {duration}")
        print(f"下载目录: {self.download_dir}")
        
        # 分析DOI格式
        self.analyze_doi_patterns()
        
        # 保存详细报告
        report_file = f"final_download_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2)
        
        print(f"详细报告: {report_file}")
    
    def analyze_doi_patterns(self):
        """分析DOI格式的成功率"""
        print("\n📈 DOI格式分析:")
        
        format_stats = {}
        for detail in self.stats['details']:
            doi = detail['doi']
            if 'S0304-8853' in doi:
                format_type = 'S0304-8853格式'
            elif 'j.jmmm' in doi:
                format_type = 'j.jmmm格式'
            else:
                format_type = '其他格式'
            
            if format_type not in format_stats:
                format_stats[format_type] = {'success': 0, 'total': 0}
            format_stats[format_type]['total'] += 1
            if detail['status'] == 'success':
                format_stats[format_type]['success'] += 1
        
        for format_type, stats in format_stats.items():
            if stats['total'] > 0:
                rate = (stats['success'] / stats['total']) * 100
                print(f"  {format_type}: {stats['success']}/{stats['total']} ({rate:.1f}%)")

def main():
    if len(sys.argv) < 2:
        print("用法: python final_doi_downloader.py <DOI文件> [下载目录] [最大下载数]")
        print("示例: python final_doi_downloader.py sample_dois.txt downloads 100")
        sys.exit(1)
    
    input_file = sys.argv[1]
    download_dir = sys.argv[2] if len(sys.argv) > 2 else "downloads"
    max_downloads = int(sys.argv[3]) if len(sys.argv) > 3 else None
    
    downloader = FinalDOIDownloader(download_dir)
    downloader.process_doi_file(input_file, max_downloads)

if __name__ == "__main__":
    main() 