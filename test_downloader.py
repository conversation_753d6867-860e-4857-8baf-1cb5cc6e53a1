#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOI下载测试器
"""

import requests
import os
import sys
import time
import re
import json
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse

class DOITester:
    def __init__(self, download_dir="downloads"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'start_time': datetime.now(),
            'details': []
        }
    
    def get_pdf_url(self, doi):
        """获取PDF链接"""
        scihub_urls = [
            f"https://sci-hub.se/{doi}",
            f"https://sci-hub.st/{doi}",
            f"https://sci-hub.ru/{doi}"
        ]
        
        for scihub_url in scihub_urls:
            try:
                print(f"  尝试: {scihub_url}")
                response = self.session.get(scihub_url, timeout=30)
                
                if response.status_code == 200:
                    # 查找PDF链接
                    pdf_pattern = r'href=["\']([^"\']*\.pdf[^"\']*)["\']'
                    matches = re.findall(pdf_pattern, response.text, re.IGNORECASE)
                    
                    for match in matches:
                        if match.startswith('http'):
                            return match
                        elif match.startswith('//'):
                            return 'https:' + match
                        elif match.startswith('/'):
                            parsed = urlparse(scihub_url)
                            return f"{parsed.scheme}://{parsed.netloc}{match}"
                        else:
                            return f"{scihub_url.rstrip('/')}/{match}"
                            
            except Exception as e:
                print(f"  ⚠️ 失败: {e}")
                continue
        
        return None
    
    def download_pdf(self, pdf_url, filename):
        """下载PDF"""
        try:
            print(f"  下载: {pdf_url}")
            response = self.session.get(pdf_url, stream=True, timeout=60)
            response.raise_for_status()
            
            file_path = self.download_dir / filename
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            file_size = file_path.stat().st_size
            if file_size > 1000:
                print(f"  ✅ 成功: {filename} ({file_size} bytes)")
                return True
            else:
                print(f"  ❌ 文件太小: {file_size} bytes")
                os.remove(file_path)
                return False
                
        except Exception as e:
            print(f"  ❌ 下载失败: {e}")
            return False
    
    def test_dois(self, input_file, max_tests=20):
        """测试DOI下载"""
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
        
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📁 测试文件: {input_file}")
        print(f"📊 总行数: {len(lines)}")
        print(f"🎯 测试数量: {min(max_tests, len(lines))}")
        print("=" * 50)
        
        test_count = 0
        for line_num, line in enumerate(lines, 1):
            if test_count >= max_tests:
                break
                
            line = line.strip()
            if not line:
                continue
            
            print(f"\n[{test_count + 1}/{max_tests}] 第{line_num}行: {line}")
            
            # 提取DOI
            doi_pattern = r'10\.\d{4,}/[-._;()/:\w]+'
            matches = re.findall(doi_pattern, line)
            
            if not matches:
                print("  ❌ 未找到DOI")
                self.stats['failed'] += 1
                continue
            
            test_count += 1
            self.stats['total'] += 1
            
            doi = matches[0]
            print(f"  🔍 DOI: {doi}")
            
            # 获取PDF链接
            pdf_url = self.get_pdf_url(doi)
            
            if not pdf_url:
                print("  ❌ 无法获取PDF链接")
                self.stats['failed'] += 1
                self.stats['details'].append({
                    'doi': doi,
                    'status': 'no_pdf_url'
                })
                continue
            
            # 下载PDF
            safe_doi = doi.replace('/', '_').replace(':', '_')
            filename = f"{safe_doi}.pdf"
            
            if self.download_pdf(pdf_url, filename):
                self.stats['success'] += 1
                self.stats['details'].append({
                    'doi': doi,
                    'status': 'success',
                    'filename': filename
                })
            else:
                self.stats['failed'] += 1
                self.stats['details'].append({
                    'doi': doi,
                    'status': 'download_failed'
                })
            
            time.sleep(2)
        
        # 生成报告
        self.print_report()
    
    def print_report(self):
        """打印统计报告"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        print("\n" + "=" * 50)
        print("📊 测试结果")
        print("=" * 50)
        print(f"总测试数: {self.stats['total']}")
        print(f"成功下载: {self.stats['success']}")
        print(f"下载失败: {self.stats['failed']}")
        
        if self.stats['total'] > 0:
            success_rate = (self.stats['success'] / self.stats['total']) * 100
            print(f"成功率: {success_rate:.1f}%")
        
        print(f"耗时: {duration}")
        print(f"下载目录: {self.download_dir}")
        
        # 保存详细报告
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2)
        
        print(f"详细报告: {report_file}")

def main():
    if len(sys.argv) < 2:
        print("用法: python test_downloader.py <DOI文件> [下载目录] [测试数量]")
        print("示例: python test_downloader.py sample_dois.txt downloads 20")
        sys.exit(1)
    
    input_file = sys.argv[1]
    download_dir = sys.argv[2] if len(sys.argv) > 2 else "downloads"
    max_tests = int(sys.argv[3]) if len(sys.argv) > 3 else 20
    
    tester = DOITester(download_dir)
    tester.test_dois(input_file, max_tests)

if __name__ == "__main__":
    main() 