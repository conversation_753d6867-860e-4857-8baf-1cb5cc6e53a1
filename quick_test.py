#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速DOI下载成功率测试
"""

import requests
import os
import sys
import time
import re
import json
from pathlib import Path
from datetime import datetime

class QuickDOITester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'no_pdf': 0,
            'details': []
        }
    
    def test_pdf_availability(self, doi):
        """测试PDF是否可用"""
        scihub_urls = [
            f"https://sci-hub.ru/{doi}",
            f"https://sci-hub.se/{doi}",
            f"https://sci-hub.st/{doi}"
        ]
        
        for scihub_url in scihub_urls:
            try:
                response = self.session.get(scihub_url, timeout=10)
                if response.status_code == 200:
                    # 检查是否有PDF链接
                    pdf_pattern = r'href=["\']([^"\']*\.pdf[^"\']*)["\']'
                    matches = re.findall(pdf_pattern, response.text, re.IGNORECASE)
                    if matches:
                        return 'available'
            except:
                continue
        
        return 'not_available'
    
    def test_dois(self, input_file, max_tests=50):
        """测试DOI的PDF可用性"""
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
        
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📁 测试文件: {input_file}")
        print(f"📊 总行数: {len(lines)}")
        print(f"🎯 测试数量: {min(max_tests, len(lines))}")
        print("=" * 50)
        
        test_count = 0
        for line_num, line in enumerate(lines, 1):
            if test_count >= max_tests:
                break
                
            line = line.strip()
            if not line:
                continue
            
            # 提取DOI
            doi_pattern = r'10\.\d{4,}/[-._;()/:\w]+'
            matches = re.findall(doi_pattern, line)
            
            if not matches:
                continue
            
            test_count += 1
            self.stats['total'] += 1
            
            doi = matches[0]
            print(f"[{test_count}/{max_tests}] {doi}")
            
            # 测试PDF可用性
            status = self.test_pdf_availability(doi)
            
            if status == 'available':
                self.stats['success'] += 1
                print("  ✅ PDF可用")
            else:
                self.stats['no_pdf'] += 1
                print("  ❌ PDF不可用")
            
            self.stats['details'].append({
                'doi': doi,
                'status': status
            })
            
            time.sleep(0.5)  # 短暂延迟
        
        self.print_report()
    
    def print_report(self):
        """打印统计报告"""
        print("\n" + "=" * 50)
        print("📊 测试结果")
        print("=" * 50)
        print(f"总测试数: {self.stats['total']}")
        print(f"PDF可用: {self.stats['success']}")
        print(f"PDF不可用: {self.stats['no_pdf']}")
        
        if self.stats['total'] > 0:
            success_rate = (self.stats['success'] / self.stats['total']) * 100
            print(f"可用率: {success_rate:.1f}%")
        
        # 按DOI格式分析
        self.analyze_formats()
        
        # 保存报告
        report_file = f"quick_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2)
        
        print(f"详细报告: {report_file}")
    
    def analyze_formats(self):
        """分析不同DOI格式的可用率"""
        print("\n📈 格式分析:")
        
        format_stats = {}
        for detail in self.stats['details']:
            doi = detail['doi']
            if 'S0304-8853' in doi:
                format_type = 'S0304-8853格式'
            elif 'j.jmmm' in doi:
                format_type = 'j.jmmm格式'
            else:
                format_type = '其他格式'
            
            if format_type not in format_stats:
                format_stats[format_type] = {'success': 0, 'total': 0}
            format_stats[format_type]['total'] += 1
            if detail['status'] == 'available':
                format_stats[format_type]['success'] += 1
        
        for format_type, stats in format_stats.items():
            if stats['total'] > 0:
                rate = (stats['success'] / stats['total']) * 100
                print(f"  {format_type}: {stats['success']}/{stats['total']} ({rate:.1f}%)")

def main():
    if len(sys.argv) < 2:
        print("用法: python quick_test.py <DOI文件> [测试数量]")
        print("示例: python quick_test.py sample_dois.txt 50")
        sys.exit(1)
    
    input_file = sys.argv[1]
    max_tests = int(sys.argv[2]) if len(sys.argv) > 2 else 50
    
    tester = QuickDOITester()
    tester.test_dois(input_file, max_tests)

if __name__ == "__main__":
    main() 